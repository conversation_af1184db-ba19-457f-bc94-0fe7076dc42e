using System;
using System.Collections.Generic;
using App.Models.Clips.Impl.Decorators.Clip;
using App.Models.Clips.Impl.Tasks.Info;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Data;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using CoreTech.Services.BinaryHttp;
using CoreTech.Services.GraphQl;

namespace App.Models.Clips.Impl.Tasks
{
    public class ClipsFetchTask : IClipsFetchTask
    {
        private readonly IClipMetadataTask clipMetadataTask;
        private readonly IPromise<IClipTrackerInternal[]> promise;
        private readonly List<IClipTrackerInternal> clipDecorators;
        private readonly IGraphQlService graphQlService;
        private readonly IBinaryHttpService binaryHttpService;
        
        public ClipsFetchTask(IGraphQlService graphQlService, IBinaryHttpService binaryHttpService)
        {
            promise = new Promise<IClipTrackerInternal[]>();
            clipDecorators = new List<IClipTrackerInternal>();
            clipMetadataTask = new ClipMetadataTask(graphQlService);

            this.graphQlService = graphQlService;
            this.binaryHttpService = binaryHttpService;
        }

        public IPromise<IClipTrackerInternal[]> Fetch(string appSyncUrl,int[] usedClips)
        {
            clipMetadataTask.Fetch(appSyncUrl, usedClips).Then(clips => OnClipsReceived(clips,appSyncUrl)).Fail(OnFail);
            
            return promise;
        }

        private void OnClipsReceived(IClipMetadata[] clips,string appSyncUrl)
        {
            var promiseChain = new PromiseChain();
            
            foreach (var clip in clips)
            {
                promiseChain.Chain(() => FetchClipInformation(clip,appSyncUrl));
            }
            
            promiseChain.Do().Then(OnComplete).Fail(OnFail);
        }

        private IPromise FetchClipInformation(IClipMetadata clip,string appSyncUrl)
        {
            //TODO find the exact reason why the bug is happening and keep only the first time from 25 frames
            var infoFetchTask = new FetchClipInformation(graphQlService, binaryHttpService);
            
            var outcome = infoFetchTask.Fetch(clip, appSyncUrl).Then(clipInfo => OnClipFetchComplete(clip,clipInfo));
            return new PromiseWrapper<IClipDecorator>(outcome);
        }
        
        private void OnFail(Exception exception)
        {
            promise.ReportFail(exception);
        }

        private void OnClipFetchComplete(IClipMetadata clipMetadata,IClipDecorator clipInfo)
        {
            var clipTracker = new ClipTracker(clipMetadata, clipInfo);
            clipDecorators.Add(clipTracker);
        }

        private void OnComplete()
        {
            promise.Dispatch(clipDecorators.ToArray());
        }
    }
}
