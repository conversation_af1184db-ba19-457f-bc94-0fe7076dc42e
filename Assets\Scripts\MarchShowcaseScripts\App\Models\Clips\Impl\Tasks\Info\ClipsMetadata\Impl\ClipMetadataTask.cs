using System;
using System.Collections.Generic;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Data;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Data.Impl;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.DataContract;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Query;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using CoreTech.Services.GraphQl;

namespace App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl
{
    public class ClipMetadataTask : IClipMetadataTask
    {
        private readonly IGraphQlService graphQlService;

        private readonly IPromise<IClipMetadata[]> promise;
        
        public ClipMetadataTask(IGraphQlService graphQlService)
        {
            promise = new Promise<IClipMetadata[]>();
            
            this.graphQlService = graphQlService;
        }
        
        public IPromise<IClipMetadata[]> Fetch(string appSyncUrl, int[] usedClips = null)
        {
            var query = new GetRandomClipsQuery(usedClips);
			
            graphQlService.GetGraphQlRequest<ClipMetadataContainerDataContract>(appSyncUrl, query).Then(FetchCompleted).Fail(OnFail);

            return promise;
        }
        
        private void FetchCompleted(ClipMetadataContainerDataContract dataContract)
        {
            var clips = new List<IClipMetadata>();
            
            foreach (var clipMetadata in dataContract.ClipMetadata)
            {
                var clip = new ClipMetadata(clipMetadata);
                clips.Add(clip);
            }
            
            promise.Dispatch(clips.ToArray());
        }

        private void OnFail(Exception exception)
        {
            promise.ReportFail(exception);
        }
    }
}
