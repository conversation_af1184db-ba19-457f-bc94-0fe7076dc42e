using System;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts;
using Framework.Utilities;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Period.Impl
{
	public class PeriodData : IPeriodData
	{
		public int Number { get; }
		public DateTime StartFrameClock { get; }
		public DateTime EndFrameClock { get; }
		public int StartFrameIdx { get; }
		public int EndFrameIdx { get; }
		public bool HomeAttPositive { get; }
		
		public PeriodData(PeriodDataContract dataContract)
		{
			Number = dataContract.Number;
			StartFrameClock = dataContract.StartFrameClock.FromUnixTime();
			EndFrameClock = dataContract.EndFrameClock.FromUnixTime();
			StartFrameIdx = dataContract.StartFrameIdx;
			EndFrameIdx = dataContract.EndFrameIdx;
			HomeAttPositive = dataContract.HomeAttPositive;
		}
	}
}
