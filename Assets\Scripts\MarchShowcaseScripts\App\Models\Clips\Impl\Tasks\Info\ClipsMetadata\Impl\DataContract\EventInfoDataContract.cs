using Newtonsoft.Json;

namespace App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.DataContract
{
	[JsonObject(MemberSerialization.OptIn)]
	public class EventInfoDataContract 
	{
		[JsonProperty("shotTakerOptaID")]
		public string ShotTakerId { get; private set; }

		[JsonProperty("shotAssisterOptaID")]
		public string ShotAssisterId { get; private set; }

		[JsonProperty("shotDeflectionPlayerOptaID")]
		public string ShotDeflectionPlayerId { get; private set; }

		[JsonProperty("goalDetected")]
		public bool GoalDetected { get; private set; }

		[JsonProperty("goalArea")]
		public string GoalArea { get; private set; }

		[JsonProperty("missedByDistance")]
		public float? MissedByDistance { get; private set; }

		[JsonProperty("shotOnTarget")]
		public bool ShotOnTarget { get; private set; }

		[JsonProperty("shotSaved")]
		public bool ShotSaved { get; private set; }

		[JsonProperty("shotMissed")]
		public bool ShotMissed { get; private set; }

		[JsonProperty("shotDistance")]
		public float ShotDistance { get; private set; }

		[JsonProperty("shotMadeAt")]
		public float ShotMadeAt { get; private set; }

		[JsonProperty("shotSpeed")]
		public float ShotSpeed { get; private set; }

		[JsonProperty("shotAngle")]
		public float ShotAngle { get; private set; }

		[JsonProperty("shotHeight")]
		public float ShotHeight { get; private set; }

		[JsonProperty("numberDefenders")]
		public int NumberDefenders { get; private set; }

		[JsonProperty("shotTakerName")]
		public string ShotTakerName { get; private set; }
	}
}
