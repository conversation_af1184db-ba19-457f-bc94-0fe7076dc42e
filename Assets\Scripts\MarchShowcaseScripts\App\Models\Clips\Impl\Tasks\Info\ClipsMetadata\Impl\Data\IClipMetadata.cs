using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Data.Impl;
using App.Types;

namespace App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Data
{
	public interface IClipMetadata
	{
		string OptaId { get; }
		string BaseUrl { get; }
		long InitialBinaryId { get; }
		string CameraTargetsUrl { get; }
		long EventWallClock { get; }
		Direction CameraPanDirection { get; }
		string EventType { get; }
		IEventInfo EventInfo { get; }
		string ClipUuid { get; }
		int ClipCounterId { get; }
		long ClipStartWallClock { get; }
		long ClipEndWallClock { get; }
		string PrimaryTeam { get; }
		string SecondaryTeam { get; }
		QuizQuestion ClipQuizQuestion { get; }
	}
}
