using System.Collections.Generic;
using CoreTech.Queries;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Query
{
	public class MetadataQuery : BaseQuery
	{
		public override string Query { get; }

		public override Dictionary<string, object> Variables { get; }

		public MetadataQuery(string optaId)
		{
			Variables = CreateVariables(optaId);
			Query = CreateQuery();
		}

		private Dictionary<string, object> CreateVariables(string optaId)
		{
			var variables = new Dictionary<string, object>()
			{
				{"optaId", optaId}
			};
			
			return variables;
		}
		
		private string CreateQuery()
		{
			return  @"query matchMetadata($optaId: String!) {
						matchMetadata(optaId: $optaId) {
							awayOptaId
							awayOptaUuid
							awayScore
							awaySsiId
							day
							description
							homeOptaId
							homeOptaUuid
							homeScore
							homeSsiId
							month
							optaId
							optaUuid
							pitchLength
							pitchWidth
							ssiId
							startTime
							venueId
							year
							awayPlayers {
								name
								optaUuid
								number
								optaId
								position
								ssiId
							}
							homePlayers {
								name
								optaUuid
								number
								optaId
								position
								ssiId
							}
							periods {
								endFrameClock
								homeAttPositive
								number
								startFrameClock
							}
						}
				}";
		}
	}
}
