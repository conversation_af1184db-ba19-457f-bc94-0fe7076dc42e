using System;
using App.Models.SecondSpectrum.Impl.Data;
using App.Models.SecondSpectrum.Impl.Services;
using App.Models.SecondSpectrum.Impl.Services.Impl;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using CoreTech.Services.BinaryHttp;

namespace App.Models.Clips.Impl.Tasks.Info.SecondSpectrum
{
    public class SecondSpectrumTask : ISecondSpectrumTask
    {
        private readonly ISecondSpectrumBinaryDeserializer binaryDeserializer;

        private readonly IPromise<ISecondSpectrumData> promise;
        
        public SecondSpectrumTask(IBinaryHttpService binaryHttpService)
        {
            promise = new Promise<ISecondSpectrumData>();
            binaryDeserializer = new SecondSpectrumBinaryDeserializer(binaryHttpService);
            binaryDeserializer.DataReceived += OnDataReceived;
        }

        ~SecondSpectrumTask()
        {
            binaryDeserializer.DataReceived -= OnDataReceived;
        }
        
        public IPromise<ISecondSpectrumData> FetchBinary(string baseUrl,long binaryId)
        {
            var url = $"{baseUrl}{binaryId}";

            binaryDeserializer.RequestBinary(url).Fail(OnFail);

            return promise;
        }
        
        private void OnDataReceived(Tuple<int[], long[], float[], byte[]> data)
        {
            var secondSpectrumData = new SecondSpectrumData(data.Item1, data.Item2, data.Item3, data.Item4);
            
            promise.Dispatch(secondSpectrumData);
        }

        private void OnFail(Exception exception)
        {
            promise.ReportFail(exception);
        }
    }
}
