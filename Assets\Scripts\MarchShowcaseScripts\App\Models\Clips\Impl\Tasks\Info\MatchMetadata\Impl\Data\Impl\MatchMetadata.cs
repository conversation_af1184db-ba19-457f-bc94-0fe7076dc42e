using System;
using System.Linq;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Period;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Period.Impl;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Players;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Players.Impl;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts;
using Framework.Utilities;
using UnityEngine;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl
{
	public class MatchMetadata : IMatchMetadata
	{
		public string VenueId { get; }
		public string Description { get; }
		public DateTime StartTime { get; }
		public DateTime Date { get; }
		public string DateText { get; }
		public Vector2 PitchDimensions { get; }
		public double Fps { get; }
		public IPeriodData[] Periods { get; }
		public IPlayerMetadata[] HomePlayers { get; }
		public IPlayerMetadata[] AwayPlayers { get; }
		public int HomeScore { get; }
		public int AwayScore { get; }
		public string SsiId { get; }
		public string OptaId { get; }
		public string OptaUuid { get; }
		public string HomeSsiId { get; }
		public string HomeOptaId { get; }
		public string HomeOptaUuid { get; }
		public string AwaySsiId { get; }
		public string AwayOptaId { get; }
		public string AwayOptaUuid { get; }
		public string HomeTeam { get; private set; }
		public string AwayTeam { get; private set; }
		
		public MatchMetadata(MatchMetadataDataContract dataContract)
		{
		
			VenueId = dataContract.VenueId;
			Description = dataContract.Description;
			StartTime = dataContract.StartTime.FromUnixTime();
			Fps = dataContract.Fps??25;
			HomeScore = dataContract.HomeScore;
			AwayScore = dataContract.AwayScore;
			SsiId = dataContract.SsiId;
			OptaId = dataContract.OptaId;
			OptaUuid = dataContract.OptaUuid;
			HomeSsiId = dataContract.HomeSsiId;
			HomeOptaId = dataContract.HomeOptaId;
			HomeOptaUuid = dataContract.HomeOptaUuid;
			AwaySsiId = dataContract.AwaySsiId;
			AwayOptaId = dataContract.AwayOptaId;
			AwayOptaUuid = dataContract.AwayOptaUuid;
			Date = new DateTime(dataContract.Year, dataContract.Month, dataContract.Day,0,0,0,0);
			DateText = Date.ToString("dddd d MMMM");
			PitchDimensions = new Vector2(dataContract.PitchLength, dataContract.PitchWidth);
			SetTeamNames(Description);
			Periods = dataContract.Periods.ToList().ConvertAll(data => new PeriodData(data)).ToArray();
			HomePlayers = dataContract.HomePlayers.ToList().ConvertAll(data => new PlayerMetadata(data,HomeTeam)).ToArray();
			AwayPlayers = dataContract.AwayPlayers.ToList().ConvertAll(data => new PlayerMetadata(data,AwayTeam)).ToArray();
		}
		
		private void SetTeamNames(string metadataMatchDescription)
		{
			var splitDescription = metadataMatchDescription.Split(" ");
			HomeTeam = splitDescription[0];
			AwayTeam = splitDescription[2];
		}
	}
}