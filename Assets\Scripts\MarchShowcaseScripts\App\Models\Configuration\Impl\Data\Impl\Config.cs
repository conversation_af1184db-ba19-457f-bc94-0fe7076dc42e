using System;
using App.Models.Configuration.Impl.Data.Impl.Betting;
using App.Models.Configuration.Impl.DataContract;
using App.Models.QrCode.Data;

namespace App.Models.Configuration.Impl.Data.Impl
{
    public class Config : IConfig
    {
        private const string DateTimeFormat = "dd'/'MM'/'yyyy";
        
        public int CacheSizeLimit { get; }
        public string AppSyncUrl { get; }
        public DateTime MatchSearchDateTime { get; }
        public int MatchSearchDays { get; }
        public BettingData Betting { get; }

        public IGlassQr GlassQr { get; set; }
        public string LiveUrl { get; }
        public string LiveWsUrl { get; }
        public string LiveWsAuthHost { get; }

        public Config(ConfigurationDataContract dataContract)
        {
            MatchSearchDays = dataContract.MatchSearchDays;
            CacheSizeLimit = dataContract.CacheSizeLimit;
            AppSyncUrl = dataContract.AppSyncUrl;
            MatchSearchDateTime = DateTime.ParseExact(dataContract.MatchSearchDate,DateTimeFormat,null);
            Betting = new BettingData(dataContract.BettingData);
            LiveUrl = dataContract.LiveUrl;
            LiveWsUrl = dataContract.LiveWsUrl;
            LiveWsAuthHost = dataContract.LiveWsAuthHost;
        }

        public void SetGlassQrData(IGlassQr glassQr)
        {
            GlassQr = glassQr;
        }

    }
}
