using Newtonsoft.Json;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts
{
	[JsonObject(MemberSerialization.OptIn)]
	public class PlayerInfoDataContract
	{
		[JsonProperty("name")]
		public string Name { get; set; }

		[JsonProperty("number")]
		public string Number { get; set; }

		[JsonProperty("position")]
		public string Position { get; set; }

		[JsonProperty("ssiId")]
		public string SsiId { get; set; }

		[JsonProperty("optaId")]
		public string OptaId { get; set; }

		[JsonProperty("optaUuid")]
		public string OptaUuid { get; set; }
	}
}