using System;
using App.Models.Clips.Impl.ClipTime;
using App.Models.Clips.Impl.Decorators.Clip;
using App.Models.Clips.Impl.Selector;
using App.Models.Clips.Impl.Tasks;
using App.Models.Configuration;
using App.Models.SecondSpectrum.Impl.Data;
using App.Types;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using Framework.Models.Base.Impl;

namespace App.Models.Clips.Impl
{
	public class ClipsModel : Model, IClipsModel
	{
		private IClipSelectorInternal clipSelector;
		private IClipStateManagement stateManagement;
		private IClipsFetchTask clipsFetchTask;

		public event Action<ClipState> StateChanged;

		public IClipSelector Selector => clipSelector;
		public ClipState ClipState => stateManagement.State;

		protected override void Initialize()
		{
			stateManagement = new ClipStateManagement();
			clipSelector = new ClipSelector(CoreTech.RouterService);
			
			clipSelector.RunningOutOfClips += OnRunningOutOfClips;
			stateManagement.StateChanged += OnStateChanged;
			CoreTech.RouterService.Subscribe<ISecondSpectrumData>(OnSecondSpectrumDataUpdate);
		}

		protected override void Dispose()
		{
			CoreTech.RouterService.Unsubscribe<ISecondSpectrumData>(OnSecondSpectrumDataUpdate);
			stateManagement.StateChanged -= OnStateChanged;
			clipSelector.RunningOutOfClips -= OnRunningOutOfClips;
		}

		public IPromise FetchClipsMetadata(int[] usedClips = null)
		{
			clipsFetchTask = new ClipsFetchTask(CoreTech.GraphQlService,CoreTech.BinaryHttpService);
			var appSyncUrl = Get<IConfigurationModel>().Configuration.AppSyncUrl;
			
			var outcome = clipsFetchTask.Fetch(appSyncUrl,usedClips).Then(OnFetchComplete);
			return new PromiseWrapper<IClipTrackerInternal[]>(outcome);
		}

		public void Play()
		{
			stateManagement.Play();
		}
		
		public void ClipComplete()
		{
			clipSelector.CompleteClip();
		}

		private void OnStateChanged(ClipState clipState)
		{
			CoreTech.RouterService.Publish(clipState);
			
			StateChanged?.Invoke(clipState);
		}
		
		private void OnSecondSpectrumDataUpdate(ISecondSpectrumData data)
		{
			var clipMetadata = Selector.CurrentClip.ClipMetadata;
			var wallClock = data.WallClock;
			
			stateManagement.Update(clipMetadata,wallClock);
			clipSelector.Update(data);
		}
		
		private void OnFetchComplete(IClipTrackerInternal[] clips)
		{
			clipSelector.AddClips(clips);
		}
		
		private void OnRunningOutOfClips()
		{
			var usedClipsIds = clipSelector.GetAllClipCounterIds();
			FetchClipsMetadata(usedClipsIds);
		}
	}
}
