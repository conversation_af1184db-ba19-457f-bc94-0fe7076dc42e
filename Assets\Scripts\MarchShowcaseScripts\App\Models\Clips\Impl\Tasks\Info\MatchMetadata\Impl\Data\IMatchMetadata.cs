using System;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Period;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Players;
using UnityEngine;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data
{
	public interface IMatchMetadata
	{ 
		string VenueId { get; }
		string Description { get; }
		DateTime StartTime { get; }
		DateTime Date { get; }
		string DateText { get; }
		Vector2 PitchDimensions { get; }
		double Fps { get; }
		IPeriodData[] Periods { get; }
		IPlayerMetadata[] HomePlayers { get; }
		IPlayerMetadata[] AwayPlayers { get; }
		string HomeTeam { get; }
		string AwayTeam { get; }
		int HomeScore { get; }
		int AwayScore { get; }
		string SsiId { get; } 
		string OptaId { get; } 
		string OptaUuid { get; }
		string HomeSsiId { get; }
		string HomeOptaId { get; }
		string HomeOptaUuid { get; }
		string AwaySsiId { get; }
		string AwayOptaId { get; }
		string AwayOptaUuid { get; }
	}
}