using System;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Query;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using CoreTech.Services.GraphQl;
using CoreTech.Services.RestService;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl
{
    public class MatchMetadataTask : IMatchMetadataTask
    {
        private readonly IGraphQlService graphQlService;
        private readonly IPromise<IMatchMetadata> promise;
        private readonly IRestService coreTechRestService;

        public MatchMetadataTask(IGraphQlService graphQlService)
        {
            this.graphQlService = graphQlService;
            promise = new Promise<IMatchMetadata>();
        }

        public MatchMetadataTask(IRestService coreTechRestService)
        {
            this.coreTechRestService = coreTechRestService;
            promise = new Promise<IMatchMetadata>();
        }

        public IPromise<IMatchMetadata> Fetch(string appSyncUrl, string optaId)
        {
            var query = new MetadataQuery(optaId);
            if (graphQlService != null)
            {
                graphQlService
                    .GetGraphQlRequest<MatchDataContract>(appSyncUrl, query)
                    .Then(FetchCompleted)
                    .Fail(OnFail);
            }
            else
            {
                coreTechRestService
                    .GetJson<MatchDataContract>(appSyncUrl)
                    .Then(FetchCompleted)
                    .Fail(OnFail);
            }

            return promise;
        }

        private void FetchCompleted(MatchDataContract dataContract)
        {
            try
            {
                var matchMetadata = new Data.Impl.MatchMetadata(dataContract.MatchMetadata);
                promise.Dispatch(matchMetadata);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        private void OnFail(Exception exception)
        {
            promise.ReportFail(exception);
        }
    }
}