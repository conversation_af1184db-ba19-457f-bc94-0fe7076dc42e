using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class TouchInput : BaseInput
    {
        public IDragEvent<float> OnPinchDrag = new DragEvent<float>();
        public IDragEvent<Vector2> OnOneFingerDrag = new DragEvent<Vector2>();
        public IDragEvent<Vector2> OnTwoFingerDrag = new DragEvent<Vector2>();
        private List<int> validTouchIds = new List<int>();
        private List<int> invalidTouchIds = new List<int>();

        protected override IEnumerator InputListener()
        {
            while (true)
            {
                UpdateValidTouchIds();
                switch (Input.touchCount)
                {
                    case 0:
                        break;
                    case 1:
                        var touch = Input.GetTouch(0);
                        if (IsTouchValid(touch))
                        {
                            var movement = GetAvgFingerMovement(new Touch[] {touch});
                            if (movement.magnitude > 0.1f) // Only process if there's meaningful movement
                            {
                                OnOneFingerDrag.Drag(movement);
                            }
                        }
                        break;
                    case 2:
                        var touch0 = Input.GetTouch(0);
                        var touch1 = Input.GetTouch(1);
                        if (IsTouchValid(touch0) && IsTouchValid(touch1))
                        {
                            var pinchValue = CalculatePinch(touch0, touch1);
                            var movement = GetAvgFingerMovement(new Touch[] {touch0, touch1});

                            // Prioritize pinch over two-finger drag
                            // If there's significant pinch movement, treat it as zoom
                            if (Mathf.Abs(pinchValue) > 1.0f) // Increased threshold for pinch detection
                            {
                                Debug.Log($"TouchInput: Pinch value calculated: {pinchValue}");
                                OnPinchDrag.Drag(pinchValue);
                            }
                            // Only process two-finger drag if pinch is minimal (for rotation)
                            else if (movement.magnitude > 0.1f && Mathf.Abs(pinchValue) < 0.5f)
                            {
                                OnTwoFingerDrag.Drag(movement);
                            }
                        }
                        break;
                    default:
                        Debug.Log("More than 2 inputs, doing nothing");
                        break;
                }
                yield return null;
            }
        }


        private void UpdateValidTouchIds()
        {
            foreach (var touch in Input.touches)
            {
                if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
                {
                    validTouchIds.Remove(touch.fingerId);
                    invalidTouchIds.Remove(touch.fingerId);
                }

                if (touch.phase.Equals(TouchPhase.Began))
                {
                    // For touch input, we'll be more permissive and allow all touches
                    // The InteractableUIArea check is more suited for mouse input
                    validTouchIds.Add(touch.fingerId);
                }
            }
        }

        private bool IsTouchValid(Touch touch)
        {
            // Simplified validation - just check if the touch is in our valid list
            // and not in the invalid list
            return validTouchIds.Contains(touch.fingerId) &&
                   !invalidTouchIds.Contains(touch.fingerId);
        }

        private Vector2 GetAvgFingerMovement(Touch[] touches)
        {
            Vector2 totalDist = Vector2.zero;
            foreach (var touch in touches)
            {
                totalDist += touch.deltaPosition;
            }
            return totalDist / touches.Length;
        }

        private float CalculatePinch(Touch touch0, Touch touch1)
        {
            var previousPos0 = touch0.position - touch0.deltaPosition;
            var previousPos1 = touch1.position - touch1.deltaPosition;

            var previousMag = (previousPos0 - previousPos1).magnitude;
            var currentMag = (touch0.position - touch1.position).magnitude;

            return currentMag - previousMag;
        }
    }
}
