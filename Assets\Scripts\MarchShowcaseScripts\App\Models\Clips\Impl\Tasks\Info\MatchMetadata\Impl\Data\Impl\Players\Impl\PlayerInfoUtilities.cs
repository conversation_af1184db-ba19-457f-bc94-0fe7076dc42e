using UnityEngine;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Players.Impl
{
    public static class PlayerInfoUtilities
    {
        public static PlayerPosition SetPlayerPosition(string playerPosition)
        {
            switch (playerPosition)
            {
                case "GK":
                    return PlayerPosition.GoalKeeper;
                case "SUB":
                    return PlayerPosition.Substitute;
                case "ST":
                    return PlayerPosition.Striker;
                case "LCB":
                    return PlayerPosition.LeftCenterBack;
                case "RCB":
                    return PlayerPosition.RightCenterBack;
                case "LWB":
                    return PlayerPosition.LeftWingBack;
                case "RWB":
                    return PlayerPosition.RightWingBack;
                case "LAM":
                    return PlayerPosition.LeftAttackMid;
                case "RAM":
                    return PlayerPosition.RightAttackMid;
                case "CAM":
                    return PlayerPosition.CenterAttackMid;
                case "LDM":
                    return PlayerPosition.LeftDefenceMid;
                case "RDM":
                    return PlayerPosition.RightDefenceMid;
                case "CDM":
                    return PlayerPosition.CenterDefenceMid;
                case "LCM":
                    return PlayerPosition.LeftCenterMid;
                case "RCM":
                    return PlayerPosition.RightCenterMid;
                case "LW":
                    return PlayerPosition.LeftWing;
                case "RW":
                    return PlayerPosition.RightWing;
                case "LB":
                    return PlayerPosition.LeftBack;
                case "RB":
                    return PlayerPosition.RightBack;
                case "CB":
                    return PlayerPosition.CenterBack;
                case "CF":
                    return PlayerPosition.CenterForward;
                case "DF":
                    return PlayerPosition.Defender;
                case "MF":
                    return PlayerPosition.MidFielder;
                case "FW":
                    return PlayerPosition.Forward;
                default:
                    Debug.LogWarning("No PlayerPosition Enum case for: " + playerPosition);
                    return (PlayerPosition.None);
            }
        }
    }
}
