using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class TouchInputMapper : IListener
    {
        private CameraMover cameraMover;
        private float zoomSpeedMultiplier = 0.1f;
        private float horizontalRotationMultiplier = 0.02f;
        private float verticalRotationMultiplier = 0.02f;
        private TouchInput touchInput;

        public TouchInputMapper(GameObject cameraComponentManager, CameraMover cameraMover)
        {
            this.cameraMover = cameraMover;
            if (!cameraComponentManager.TryGetComponent<TouchInput>(out touchInput))
            {
                touchInput = cameraComponentManager.AddComponent<TouchInput>();
            }
        }

        public void StartListening()
        {
            touchInput.StartListening();
            touchInput.OnPinchDrag.OnDrag += HandlePinch;
            touchInput.OnOneFingerDrag.OnDrag += HandleOneFingerDrag;
            touchInput.OnOneFingerDrag.OnDragEnd += HandleOneFingerDragEnd;
            touchInput.OnTwoFingerDrag.OnDrag += HandleTwoFingerDrag;
            touchInput.OnTwoFingerDrag.OnDragEnd += HandleTwoFingerDragEnd;
        }

        public void StopListening()
        {
            touchInput.StopListening();
            touchInput.OnPinchDrag.OnDrag -= HandlePinch;
            touchInput.OnOneFingerDrag.OnDrag -= HandleOneFingerDrag;
            touchInput.OnOneFingerDrag.OnDragEnd -= HandleOneFingerDragEnd;
            touchInput.OnTwoFingerDrag.OnDrag -= HandleTwoFingerDrag;
            touchInput.OnTwoFingerDrag.OnDragEnd -= HandleTwoFingerDragEnd;
        }

        private void HandleOneFingerDrag(Vector2 vector)
        {
            cameraMover.HorizontalRotation(-vector.x * horizontalRotationMultiplier);
            cameraMover.VerticalRotation(-vector.y * verticalRotationMultiplier);
        }

        private void HandleOneFingerDragEnd()
        {
            // Reset camera input axis to 0
            cameraMover.HorizontalRotation(0);
            cameraMover.VerticalRotation(0);
        }

        private void HandleTwoFingerDrag(Vector2 vector)
        {
            // Use two-finger drag for camera rotation (similar to one finger but maybe with different sensitivity)
            cameraMover.HorizontalRotation(-vector.x * horizontalRotationMultiplier);
            cameraMover.VerticalRotation(-vector.y * verticalRotationMultiplier);
        }

        private void HandleTwoFingerDragEnd()
        {
            // Reset camera input axis to 0
            cameraMover.HorizontalRotation(0);
            cameraMover.VerticalRotation(0);
        }

        private void HandlePinch(float value)
        {
            Debug.Log($"Pinch detected: {value}, adjusted: {value * zoomSpeedMultiplier}");
            cameraMover.AdjustZoom(value * zoomSpeedMultiplier);
        }
    }
}
