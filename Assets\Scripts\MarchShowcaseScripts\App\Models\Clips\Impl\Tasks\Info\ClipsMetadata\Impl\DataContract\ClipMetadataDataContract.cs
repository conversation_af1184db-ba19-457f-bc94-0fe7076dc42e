using Newtonsoft.Json;

namespace App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.DataContract
{
	[JsonObject(MemberSerialization.OptIn)]
	public class ClipMetadataDataContract 
	{
		[JsonProperty("gameID")]
		public int OptaId { get; private set; }
		
		[JsonProperty("baseUrl")]
		public string BaseUrl { get; private set; }
		
		[JsonProperty("initialBinaryId")]
		public long InitialBinaryId { get; private set; }
		
		[JsonProperty("cameraTargetsUrl")]
		public string CameraTargetsUrl { get; private set; }

		[JsonProperty("wallClock")]
		public long EventWallClock { get; private set; }
			
		[JsonProperty("cameraPanDirection")]
		public string CameraPanDirection { get; private set; }
		
		[JsonProperty("eventType")]
		public string EventType { get; private set; }
		
		[JsonProperty("eventInfo")]
		public EventInfoDataContract EventInfo { get; private set; }
		
		[JsonProperty("clipUuid")]
		public string ClipUuid { get;  private set;}
		
		[JsonProperty("clipCounterId")]
		public int ClipCounterId { get;  private set;}
		
		[JsonProperty("clipStart")]
		public long ClipStartWallClock { get; private set; }
		
		[JsonProperty("clipEnd")]
		public long ClipEndWallClock { get; private set; }
		
		[JsonProperty("primaryTeam")]
		public string PrimaryTeam { get; private set; }
		
		[JsonProperty("secondaryTeam")]
		public string SecondaryTeam { get;  private set;}
		
		[JsonProperty("quizQuestions")]
		public QuizQuestionDataContract[] QuizQuestions { get; private set; }
	}
}
