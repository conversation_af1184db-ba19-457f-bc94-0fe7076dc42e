using System;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Players.Impl
{
    public class PlayerMetadata : IPlayerMetadata
    {
        public string Name { get; }
        public int Number { get; }
        public PlayerPosition Position { get; }
        public string SsiId { get; }
        public string OptaId { get; }
        public string OptaUuid { get; }
        public string Team { get; }

        public PlayerMetadata(PlayerInfoDataContract dataContract,string team)
        {
            Name = dataContract.Name;
            int.TryParse(dataContract.Number, out var num);
            Number = num;
            SsiId = dataContract.SsiId;
            OptaId = dataContract.OptaId;
            OptaUuid = dataContract.OptaUuid;
            Team = team;
            Position = PlayerInfoUtilities.SetPlayerPosition(dataContract.Position);
        }
    }
}
