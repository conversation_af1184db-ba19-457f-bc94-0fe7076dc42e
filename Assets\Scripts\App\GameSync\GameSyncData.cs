using App.Components.UI.Timeline;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data;
using App.Models.MatchMetaData.Impl;
using App.Models.SecondSpectrum.Impl;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using UnityEngine;

namespace App.GameSync
{
    public class GameSyncData : MonoBehaviour
    {
        private string gameId;
        private long binaryId;
        private string baseUrl;
        private SecondSpectrumFrameModel secondSpectrumFrameModel;

        private void Awake()
        {
            // TODO : Replace with a more robust way to get these values
            gameId = PlayerPrefs.GetString("GameId");
            if(string.IsNullOrEmpty(gameId))
            {
                Debug.LogError("GameId is not set in PlayerPrefs. Please set it before using GameSyncData.");
            }

            binaryId = long.Parse(PlayerPrefs.GetString("BinaryId"));
            if (binaryId == 0)
            {
                Debug.LogError("BinaryId is not set in PlayerPrefs. Please set it before using GameSyncData.");
            }

            baseUrl = PlayerPrefs.GetString("BaseURL");
            if (string.IsNullOrEmpty(baseUrl))
            {
                Debug.LogError("BaseURL is not set in PlayerPrefs. Please set it before using GameSyncData.");
            }
        }

        public IPromise StartFetching(SecondSpectrumFrameModel secondSpectrumFrameModel)
        {
            this.secondSpectrumFrameModel = secondSpectrumFrameModel;
            var url = $"{baseUrl}/{gameId}/tracking-pose/";
            return secondSpectrumFrameModel
                .StartFetching(binaryId, url)
                .Then(() => Debug.Log("Fetching Initial Binary Data Completed"));
        }

        public IPromise LoadGameMetaData(MatchMetaDataModel matchModel)
        {
            var metadataUrl = $"{baseUrl}/{gameId}/tracking-pose/metadata.json";
            return new PromiseWrapper<IMatchMetadata>(matchModel.Fetch(metadataUrl, ""));
        }

        public void InitTimeline(TimelineDataService timelineDataService)
        {
            timelineDataService.Initialize(binaryId);
        }

        public void NextBinary(long nextBinaryId)
        {
            secondSpectrumFrameModel.SetNextBinaryId(nextBinaryId);
        }
    }
}