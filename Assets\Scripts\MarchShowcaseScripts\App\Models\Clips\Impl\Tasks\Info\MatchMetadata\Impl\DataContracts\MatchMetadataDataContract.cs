using Newtonsoft.Json;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts
{
	[JsonObject(MemberSerialization.OptIn)]
	public class MatchMetadataDataContract
	{
		[JsonProperty("venueId")]
        public string VenueId { get; private set; }
        
        [JsonProperty("description")]
        public string Description { get; private set; }
        
        [JsonProperty("startTime")]
        public long StartTime { get; private set; }
        
        [JsonProperty("year")]
        public int Year { get; private set; }
        
        [JsonProperty("month")]
        public int Month { get; private set; }
        
        [JsonProperty("day")]
        public int Day { get; private set; }
        
        [JsonProperty("pitchLength")]
        public float PitchLength { get; private set; }
        
        [JsonProperty("pitchWidth")]
        public float PitchWidth { get; private set; }
        
        [JsonProperty("homeScore")]
        public int HomeScore { get; private set; }
        
        [JsonProperty("awayScore")]
        public int AwayScore { get; private set; }
        
        [JsonProperty("ssiId")]
        public string SsiId { get; private set; }
        
        [JsonProperty("optaId")]
        public string OptaId { get; private set; }
        
        [JsonProperty("optaUuid")]
        public string OptaUuid { get; private set; }
        
        [JsonProperty("homeSsiId")]
        public string HomeSsiId { get; private set; }
        
        [JsonProperty("homeOptaId")]
        public string HomeOptaId { get; private set; }
        
        [JsonProperty("homeOptaUuid")]
        public string HomeOptaUuid { get; private set; }
        
        [JsonProperty("awaySsiId")]
        public string AwaySsiId { get; private set; }
        
        [JsonProperty("awayOptaId")]
        public string AwayOptaId { get; private set; }
        
        [JsonProperty("awayOptaUuid")]
        public string AwayOptaUuid { get; private set; }
        
        [JsonProperty("fps")]
        public double? Fps { get; private set; }

        [JsonProperty("periods")]
        public PeriodDataContract[] Periods { get; private set; }
        
        [JsonProperty("homePlayers")]
        public PlayerInfoDataContract[] HomePlayers { get; private set; }
        
        [JsonProperty("awayPlayers")]
        public PlayerInfoDataContract[] AwayPlayers { get; private set; }
	}
}