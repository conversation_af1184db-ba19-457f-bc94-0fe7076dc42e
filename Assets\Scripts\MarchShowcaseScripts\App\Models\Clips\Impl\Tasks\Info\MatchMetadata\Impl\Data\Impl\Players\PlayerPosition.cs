namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data.Impl.Players
{
    //Note: this may not yet be an exhaustive list and may need adding to
    public enum PlayerPosition
    {
        None,
        Substitute,
        Goal<PERSON>eeper,
        Striker,
        LeftCenterBack,
        RightCenterBack,
        LeftWingBack,
        RightWingBack,
        LeftAttackMid,
        RightAttackMid,
        CenterAttackMid,
        LeftDefenceMid,
        RightDefenceMid,
        CenterDefenceMid,
        LeftCenterMid,
        RightCenterMid,
        LeftWing,
        RightWing,
        LeftBack,
        RightBack,
        CenterBack,
        CenterForward,
        Defender,
        MidFielder,
        Forward
    }
}