using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class MouseInputMapper : IListener
    {
        private MouseInput mouseInput;
        private CameraMover cameraMover;
        private float zoomSpeedMultiplier = 0.05f;
        private float horizontalRotationMultiplier = 0.02f;
        private float verticalRotationMultiplier = 0.02f;


        public MouseInputMapper(GameObject cameraComponentManager, CameraMover cameraMover)
        {
            this.cameraMover = cameraMover;
            if (!cameraComponentManager.TryGetComponent<MouseInput>(out mouseInput))
            {
                mouseInput = cameraComponentManager.AddComponent<MouseInput>();
            }
        }

        public void StartListening()
        {
            mouseInput.StartListening();
            mouseInput.OnRightClickDrag.OnDrag += HandleRightClickDrag;
            mouseInput.OnRightClickDrag.OnDragEnd += HandleRightClickDragEnd;
            mouseInput.OnMiddleMouseDrag.OnDrag += HandleMiddleMouseDrag;
        }

        public void StopListening()
        {
            mouseInput.StopListening();
            mouseInput.OnRightClickDrag.OnDrag -= HandleRightClickDrag;
            mouseInput.OnRightClickDrag.OnDragEnd -= HandleRightClickDragEnd;
            mouseInput.OnMiddleMouseDrag.OnDrag -= HandleMiddleMouseDrag;
        }

        private void HandleRightClickDrag(Vector2 vector)
        {
            cameraMover.HorizontalRotation(-vector.x * horizontalRotationMultiplier);
            cameraMover.VerticalRotation(-vector.y * verticalRotationMultiplier);
        }
        private void HandleRightClickDragEnd()
        {
            // Reset camera input axis to 0
            cameraMover.HorizontalRotation(0);
            cameraMover.VerticalRotation(0);
        }

        private void HandleMiddleMouseDrag(Vector2 vector)
        {
            float value = -vector.y;
            cameraMover.AdjustZoom(value * zoomSpeedMultiplier);
        }
    }
}
