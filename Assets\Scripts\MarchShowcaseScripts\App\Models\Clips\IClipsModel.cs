using System;
using App.Models.Clips.Impl.Selector;
using App.Types;
using CoreTech.Promises;
using Framework.Models.Base;

namespace App.Models.Clips
{
	public interface IClipsModel : IModel
	{
		event Action<ClipState> StateChanged;
		
		IClipSelector Selector { get; }
		ClipState ClipState { get; }
		
		IPromise FetchClipsMetadata(int[] usedClips = null);
		void Play();
		void ClipComplete();
	}
}
