using Newtonsoft.Json;

namespace App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.DataContract
{
	[JsonObject(MemberSerialization.OptIn)]
	public class QuizQuestionDataContract 
	{
		[JsonProperty("pausePoint")]
		public long PausePoint { get; private set; }
		
		[Json<PERSON>roperty("questionUuid")]
		public string QuestionUuid { get; private set; }
		
		[JsonProperty("questionType")]
		public string QuestionType { get; private set; }
		
		[JsonProperty("question")]
		public string Question { get; private set; }
		
		[JsonProperty("questionContext")]
		public QuestionContextDataContract QuestionContext { get; private set; }
		
		[JsonProperty("answers")]
		public AnswerDataContract[] Answers { get; private set; }
	}
}
