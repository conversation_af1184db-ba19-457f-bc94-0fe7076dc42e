using System;
using System.Collections.Generic;
using CoreTech.Queries;

namespace App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Query
{
	public sealed class GetRandomClipsQuery : BaseQuery
	{
		public override string Query { get; }

		public override Dictionary<string, object> Variables { get; }

		public GetRandomClipsQuery(int[] usedClips)
		{
			Variables = CreateVariables(usedClips);
			Query = CreateQuery();
		}

		private Dictionary<string, object> CreateVariables(int[] usedClips = null)
		{
			var variables = new Dictionary<string, object>();

			usedClips ??= Array.Empty<int>();
			
			variables.Add("usedClips", usedClips);
			
			return variables;
		}
		
		private string CreateQuery()
		{
			return  @"query getRandomClips($usedClips: [Int!]!) {
						getRandomClips(usedClips: $usedClips) {
							baseUrl
							initialBinaryId
							cameraTargetsUrl
							cameraPanDirection
							clipEnd
							clipStart
							wallClock
							clipUuid
							clipCounterId
							eventType
							gameID
							primaryTeam
							secondaryTeam
							eventInfo {
								goalArea
								goalDetected
								missedByDistance
								numberDefenders
								shotAngle
								shotAssisterOptaID
								shotDeflectionPlayerOptaID
								shotDistance
								shotHeight
								shotMadeAt
								shotMissed
								shotOnTarget
								shotSaved
								shotSpeed
								shotTakerName
								shotTakerOptaID
							}
							quizQuestions {
								pausePoint
								questionUuid
								questionType
								question
								questionContext {
									... on GoalPositionContext {
										gridRows
										gridColumns
									}
								}
							answers {
								answer
								correct
							}
						}
					}
				}";
		}
	}
}