using Newtonsoft.Json;

namespace App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.DataContracts
{
	[JsonObject(MemberSerialization.OptIn)]
	public class PeriodDataContract
	{
		[JsonProperty("number")]
		public int Number { get; set; }

		[JsonProperty("startFrameClock")]
		public long StartFrameClock { get; set; }

		[Json<PERSON>roperty("endFrameClock")]
		public long EndFrameClock { get; set; }

		[JsonProperty("homeAttPositive")]
		public bool HomeAttPositive { get; set; }
		
		// [JsonProperty("startFrameIdx")]
		public int StartFrameIdx { get;  set; }

		// [JsonProperty("endFrameIdx")]
		public int EndFrameIdx { get; set; }
		
	}
}