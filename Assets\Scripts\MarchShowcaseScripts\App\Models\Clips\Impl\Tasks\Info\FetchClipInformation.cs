using System;
using App.Models.Clips.Impl.Decorators.Clip;
using App.Models.Clips.Impl.Tasks.Info.ClipsMetadata.Impl.Data;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data;
using App.Models.Clips.Impl.Tasks.Info.SecondSpectrum;
using App.Models.SecondSpectrum.Impl.Data;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using CoreTech.Services.BinaryHttp;
using CoreTech.Services.GraphQl;

namespace App.Models.Clips.Impl.Tasks.Info
{
    public class FetchClipInformation : IFetchClipInformation
    {
        private readonly IMatchMetadataTask matchMetadataTask;
        private readonly ISecondSpectrumTask secondSpectrumTask;
        
        private readonly IPromise<IClipDecorator> promise;

        private IMatchMetadata matchMetadata;
        private ISecondSpectrumData secondSpectrumData;
        
        public FetchClipInformation(IGraphQlService graphQlService, IBinaryHttpService binaryHttpService)
        {
            promise = new Promise<IClipDecorator>();
            matchMetadataTask = new MatchMetadataTask(graphQlService);
            secondSpectrumTask = new SecondSpectrumTask(binaryHttpService);
        }

        public IPromise<IClipDecorator> Fetch(IClipMetadata clip, string appSyncUrl)
        {
            var promiseChain = new PromiseChain();
            
            promiseChain
                .Chain(() => FetchMatchMetadata(appSyncUrl, clip))
                .Chain(() => FetchSecondSpectrum(clip.BaseUrl,clip.InitialBinaryId));
            
            promiseChain.Do().Then(OnComplete).Fail(OnFail);

            return promise;
        }

        private IPromise FetchMatchMetadata(string appSyncUrl,IClipMetadata clipMetadata)
        {
            var outcome = matchMetadataTask.Fetch(appSyncUrl, clipMetadata.OptaId).Then(MatchMetadataReceived);
            return new PromiseWrapper<IMatchMetadata>(outcome);
        }

        private IPromise FetchSecondSpectrum(string url, long initialBinaryId)
        {
            var outcome = secondSpectrumTask.FetchBinary(url, initialBinaryId).Then(SecondSpectrumDataReceived);
            return new PromiseWrapper<ISecondSpectrumData>(outcome);
        }
        
        private void MatchMetadataReceived(IMatchMetadata matchMetadata)
        {
            this.matchMetadata = matchMetadata;
        }

        private void SecondSpectrumDataReceived(ISecondSpectrumData secondSpectrumData)
        {
            this.secondSpectrumData = secondSpectrumData;
        }
        
        private void OnComplete()
        {
            var decorator = new ClipDecorator(matchMetadata, secondSpectrumData);
            promise.Dispatch(decorator);
        }

        private void OnFail(Exception exception)
        {
            promise.ReportFail(exception);
        }
    }
}
